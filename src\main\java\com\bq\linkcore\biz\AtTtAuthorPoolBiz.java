package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorPoolMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class AtTtAuthorPoolBiz {

    @Resource
    private AtTiktokAuthorPoolMapper tiktokAuthorPoolMapper;

    /**
     * 插入作者池记录
     * @param authorPoolDO 作者池实体对象
     * @return 插入影响的行数
     */
    public int insertAuthorPool(AtTiktokAuthorPoolDO authorPoolDO) {
        return tiktokAuthorPoolMapper.insert(authorPoolDO);
    }

    /**
     * 根据作者ID查询记录是否存在
     * @param authorId 作者唯一ID
     * @return 记录数量，大于0表示存在
     */
    public int queryAuthorPoolExistsByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0);
        return tiktokAuthorPoolMapper.selectCount(queryWrapper);
    }

    /**
     * 根据ID更新作者池记录
     * @param authorPoolDO 作者池实体对象（需要包含ID）
     * @return 更新影响的行数
     */
    public int updateAuthorPool(AtTiktokAuthorPoolDO authorPoolDO) {
        return tiktokAuthorPoolMapper.updateById(authorPoolDO);
    }

    /**
     * 根据作者ID查询作者池记录
     * @param authorId 作者唯一ID
     * @return 作者池实体对象，不存在则返回null
     */
    public AtTiktokAuthorPoolDO queryAuthorPoolByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<AtTiktokAuthorPoolDO>()
                .eq(AtTiktokAuthorPoolDO::getAuthorId, authorId)
                .eq(AtTiktokAuthorPoolDO::getIsDel, 0);
        return tiktokAuthorPoolMapper.selectOne(queryWrapper);
    }
}
