package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorSearchResultDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorSearchResultMapper;
import org.springframework.stereotype.Component;

@Component
public class AtTikTokSearchBiz {

    private AtTiktokAuthorSearchResultMapper atTiktokAuthorSearchResultMapper;

    public int insert(AtTiktokAuthorSearchResultDO atTiktokAuthorSearchResultDO){
        return atTiktokAuthorSearchResultMapper.insert(atTiktokAuthorSearchResultDO);
    }

    public int update(AtTiktokAuthorSearchResultDO atTiktokAuthorSearchResultDO){
        return atTiktokAuthorSearchResultMapper.updateById(atTiktokAuthorSearchResultDO);
    }


    public AtTiktokAuthorSearchResultDO queryAuthor(String homeUrl){
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> wrapper = new  LambdaQueryWrapper<AtTiktokAuthorSearchResultDO>()
                .eq(AtTiktokAuthorSearchResultDO::getHomeUrl, homeUrl)
                .eq(AtTiktokAuthorSearchResultDO::getIsDel, 0);

        return atTiktokAuthorSearchResultMapper.selectOne(wrapper);
    }


}
