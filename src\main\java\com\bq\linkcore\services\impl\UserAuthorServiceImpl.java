package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.AuthorProfileVo;
import com.bq.linkcore.biz.AtTtSearchBiz;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.services.IUserAuthorService;
import com.bq.linkcore.utils.TikTokHelper;
import lombok.val;

import javax.annotation.Resource;

public class UserAuthorServiceImpl implements IUserAuthorService {

    @Resource
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;

    @Resource
    private AtTtSearchBiz ttSearchBiz;

    @Override
    public ResponseData<AuthorProfileVo> searchAuthor(Long userId, String homeUrl, String platform) {

        String uniqueId = TikTokHelper.regUniqueId(homeUrl);

        return null;
    }

    @Override
    public ResponseData addTxAuthorMonitor(Long userId, String authorId) {
        return null;
    }
}
