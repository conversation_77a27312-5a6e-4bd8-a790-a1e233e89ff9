package com.bq.linkcore.services.impl;

import com.bq.data.base.bean.ResponseData;
import com.bq.linkcore.bean.vo.AuthorProfileVo;
import com.bq.linkcore.client.tikhub.tiktok.TikHubTiktokAccountRequester;
import com.bq.linkcore.services.IUserAuthorService;

import javax.annotation.Resource;

public class UserAuthorServiceImpl implements IUserAuthorService {

    @Resource
    private TikHubTiktokAccountRequester tikHubTiktokAccountRequester;

    @Resource
    private A

    @Override
    public ResponseData<AuthorProfileVo> searchAuthor(Long userId, String homeUrl, String platform) {


        return null;
    }

    @Override
    public ResponseData addTxAuthorMonitor(Long userId, String authorId) {
        return null;
    }
}
