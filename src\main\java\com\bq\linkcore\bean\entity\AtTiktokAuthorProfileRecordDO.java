package com.bq.linkcore.bean.entity;


import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import java.io.Serializable;


import lombok.Builder;
import lombok.Data;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 * <AUTHOR>
 * @date: 2025-07-13 16:35:54
 * @ClassName: AtTiktokUserProfileRecordDO
 * @Description: tiktok 用户主页数据
 *
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@TableName("at_tiktok_author_profile_record")
public class AtTiktokAuthorProfileRecordDO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @TableId(value="id", type= IdType.AUTO)
    private Long id;

    /**
     * 唯一ID
     */
    private String authorId;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String desc;

    /**
     * 主页地址
     */
    private String homeUrl;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 作品数
     */
    private Long resourceCnt;

    /**
     * 获赞
     */
    private Integer likeCount;

    /**
     * 粉丝
     */
    private Integer fansCount;

    /**
     * 分享
     */
    private Integer shareCount;

    /**
     * 关注数
     */
    private Integer followingCount;

    /**
     * 是否认证（0未认证，1已认证）
     */
    private Integer isVerified;

    /**
     * 地区
     */
    private String registryLocation;

    /**
     * 注册时间
     */
    private Integer registerTime;

    /**
     * 加密ID
     */
    private String secUid;

    /**
     * 创建者
     */
    private Long creator;

    /**
     * 数据创建时间
     */
    private LocalDateTime createTime;

    /**
     * 数据更新人
     */
    private Long updater;

    /**
     * 数据更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 是否删除（0未删除，1已删除）
     */
    private Integer isDel;




}
