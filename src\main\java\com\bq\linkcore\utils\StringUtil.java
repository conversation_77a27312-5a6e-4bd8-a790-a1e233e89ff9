package com.bq.linkcore.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URL;
import java.net.URLDecoder;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
public class StringUtil {

    private static final List<Long> saleRange = new ArrayList<Long>(Arrays.asList(0L, 25L, 50L, 75L, 100L,
            250L, 500L, 750L, 1000L, 2500L, 5000L, 7500L, 10000L, 25000L, 50000L, 75000L, 100000L, 250000L, 500000L,
            1000000L, 2500000L, 5000000L, 7500000L, 10000000L, 25000000L, 50000000L, 75000000L));

    private static final Map<String, String> cityMap = new HashMap<String, String>() {{
        put("beijing", "北京");
        put("tianjin", "天津");
        put("shanghai", "上海");
        put("chongqing", "重庆");
        put("hebei", "河北");
        put("shanxi", "山西");
        put("liaoning", "辽宁");
        put("jilin", "吉林");
        put("heilongjiang", "黑龙江");
        put("jiangsu", "江苏");
        put("zhejiang", "浙江");
        put("anhui", "安徽");
        put("fujian", "福建");
        put("jiangxi", "江西");
        put("shandong", "山东");
        put("henan", "河南");
        put("hubei", "湖北");
        put("hunan", "湖南");
        put("guangdong", "广东");
        put("hainan", "海南");
        put("sichuan", "四川");
        put("guizhou", "贵州");
        put("yunnan", "云南");
        put("shaanxi", "陕西");
        put("gansu", "甘肃");
        put("qinghai", "青海");
        put("taiwan", "台湾");
        put("neimenggu", "内蒙古");
        put("guangxi", "广西");
        put("xizang", "西藏");
        put("ningxia", "宁夏");
        put("xinjiang", "新疆");
        put("xianggang", "香港");
        put("aomen", "澳门");
    }};

    public static String leftTrim(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }

        return str.replaceAll("^\\s+", "");
    }

    public static String rightTrim(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }

        return str.replaceAll("\\s+$", "");
    }

    public static String middleTrim(String str) {
        if (StringUtils.isBlank(str)) {
            return "";
        }

        String s1 = str.trim();
        int i1 = str.indexOf(s1);
        String s2 = str.substring(0, i1);
        String s3 = str.substring(i1 + s1.length());
        str = str.replace(" ", "");

        return s2 + str + s3;
    }

//    /**
//     * 替换字符串
//     *
//     * @param oriStr     原始字符串
//     * @param regex      是否正则表达
//     * @param matchStr   匹配字符串或正则表达式
//     * @param repStr     要替换成的字符串
//     * @param ignoreCase 是否忽略大消息
//     * @param matchAll   是否匹配所有
//     * @param n          替换第几个匹配项
//     * @return 替换后的字符串
//     */
//    public static String replaceStr(String oriStr, boolean regex, String matchStr, String repStr, boolean ignoreCase, boolean matchAll, int n) {
//        if (regex) {
//            //正则表达式
//            Pattern pattern = Pattern.compile(matchStr, ignoreCase ? Pattern.CASE_INSENSITIVE : 0);
//            Matcher matcher = pattern.matcher(oriStr);
//            boolean match = false;
//            for (int i = 0; i < n; i++) {
//                if (matcher.find()) {
//                    matchStr = matcher.group();
//                    match = true;
//                }
//            }
//            if (!match) {
//                return oriStr;
//            }
//        }
//
//        //是否限制匹配项数目
//        if (matchAll) {
//            if (ignoreCase) {
//                return StrUtil.replaceIgnoreCase(oriStr, matchStr, repStr);
//            }
//            return oriStr.replaceAll(matchStr, repStr);
//        }
//
//        //查找待替换项索引位置
//        int index;
//        if (ignoreCase) {
//            index = StringUtils.ordinalIndexOf(oriStr.toLowerCase(), matchStr.toLowerCase(), n);
//        } else {
//            index = StringUtils.ordinalIndexOf(oriStr, matchStr, n);
//        }
//
//        //替换字符串
//        if (index >= 0) {
//            String s1 = oriStr.substring(0, index);
//            String s2 = oriStr.substring(index + matchStr.length());
//            return s1 + repStr + s2;
//        }
//
//        return oriStr;
//    }

    /**
     * 按照字节数分割字符串
     *
     * @param src        字符串
     * @param byteLength 字节数
     * @return
     */
    public static String splitBytes(String src, int byteLength) {
        try {
            boolean flag = false;

            int realLen = 0;
            StringBuffer sb = new StringBuffer();
            for (int index = 0; index < src.length(); index++) {
                String subStr = src.substring(index, index + 1);
                int charLen = getCharacterBytes(subStr);
                if (charLen == 2) {
                    flag = !flag;
                }

                realLen += charLen;
                if (flag) {
                    if (realLen + 2 > byteLength) {
                        break;
                    }
                } else {
                    if (realLen > byteLength) {
                        break;
                    }
                }

                sb.append(subStr);
            }

            return sb.toString();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 按照字节数分割字符串，获取所有分割后的字符list
     *
     * @param src
     * @param byteLength
     * @return
     */
    public static List<String> splitAllBytes(String src, int byteLength) {
        List<String> contents = new ArrayList<>();

        String tmp = src;
        do {
            String subStr = splitBytes(tmp, byteLength);
            if (StringUtils.isBlank(subStr)) {
                return null;
            }

            contents.add(subStr);
            if (subStr.length() >= tmp.length()) {
                break;
            }

            tmp = tmp.substring(subStr.toCharArray().length);
        } while (true);

        return contents;
    }

    /**
     * 按照字节数分割字符串
     *
     * @param src        字符串
     * @param byteLength 字节数
     * @return
     */
    public static String autopaasSplitBytes(String src, int byteLength) {
        try {
            boolean flag = false;

            int realLen = 0;
            StringBuffer sb = new StringBuffer();
            for (int index = 0; index < src.length(); index++) {
                String subStr = src.substring(index, index + 1);
                int charLen = getCharacterBytes(subStr);
                if (charLen == 2) {
                    flag = !flag;
                }

                realLen += charLen;
                if (flag) {
                    if (realLen + 2 > byteLength) {
                        break;
                    }
                } else {
                    if (realLen > byteLength) {
                        break;
                    }
                }

                sb.append(subStr);
            }

            return sb.toString();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 支持utf8
     *
     * @param str
     * @return
     */
    private static int getCharacterBytes(String str) {
        if (isUtf8Character(str)) {
            return 3;
        }

        return 2;
    }

    /**
     * utf8
     *
     * @param str
     * @return
     */
    private static boolean isUtf8Character(String str) {
        int len = str.getBytes().length;
        return (len == 3);
    }

    /**
     * 按照字节数分割字符串，获取所有分割后的字符list，支持utf8mb4
     *
     * @param src
     * @param byteLength
     * @return
     */
    public static List<String> autopaasSplitAllBytes(String src, int byteLength) {
        List<String> contents = new ArrayList<>();

        String tmp = src;
        do {
            String subStr = autopaasSplitBytes(tmp, byteLength);
            if (StringUtils.isBlank(subStr)) {
                return null;
            }

            contents.add(subStr);
            if (subStr.length() >= tmp.length()) {
                break;
            }

            tmp = tmp.substring(subStr.toCharArray().length);
        } while (true);

        return contents;
    }

    public static Integer strToInteger(String s) {
        if (StringUtils.isBlank(s)) {
            return 0;
        }

        try {
            return Integer.parseInt(s);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return 0;
    }

    public static Long strToLong(String s) {
        if (StringUtils.isBlank(s)) {
            return 0L;
        }

        try {
            return Long.parseLong(s);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return 0L;
    }

    public static Double strToDouble(String s) {
        if (StringUtils.isBlank(s)) {
            return 0.0;
        }

        try {
            return Double.parseDouble(s);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return 0.0;
    }

    public static String formatNumber(double n) {
        String d = "0";
        if (n == 0.0) {
            return d;
        }

        if (n > 100000000) {
            double number = n / 100000000;
            d = String.format("%.1fy", number);

            return d;
        }


        if (n >= 10000) {
            double number = n / 10000;
            d = String.format("%.0fw", number);

            return d;
        }

        d = String.valueOf(Math.round(n));

        return d;
    }


    public static String formatNumberInner(double dn) {
        int index = 0;
        int len = saleRange.size();
        long n = Math.round(dn);

        for (; index < len; index++) {
            if (n >= saleRange.get(index)) {
                continue;
            } else {
                break;
            }
        }

        String s = "";
        long d1 = saleRange.get(index - 1);
        if (index >= len) {
            s = ">" + formatNumber(d1);
        } else {
            long d2 = saleRange.get(index);
            s = formatNumber(d1) + "~" + formatNumber(d2);
        }

        return s;
    }

    public static String formatUVNumber(double dn) {
        if (dn == 0.0) {
            return "0~1";
        }

        double f = Math.floor(dn);
        double c = Math.ceil(dn);

        return formatNumber(f) + "~" + formatNumber(c);
    }

//    public static List<String> segment(String text) {
//        List<Term> termList = HanLP.segment(text);
//        List<String> wordList = new ArrayList<>();
//
//        termList.forEach(term -> {
//            if (term.word.length() > 1) {
//                wordList.add(term.word);
//            }
//        });
//
//        return wordList;
//    }

    public static String parseDouyinVideoId(String videoUrl) {
        try {
            URL url = new URL(videoUrl);
            String host = url.getHost();
            if (!host.endsWith("douyin.com")) {
                return null;
            }

            String query = url.getQuery();
            Map<String, String> params = new HashMap<>();
            if (query != null) {
                String[] keyValuePairs = query.split("&");
                for (String keyValuePair : keyValuePairs) {
                    String[] keyValue = keyValuePair.split("=");
                    String key = keyValue[0];
                    String value = keyValue[1];
                    params.put(key, URLDecoder.decode(value, "UTF-8"));
                }
            }

            return params.get("modal_id");

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

        return null;
    }
//
//    public static PredictResult reviewAnalysis(String content) {
//        try {
//            String accessKeyId = "LTAI5tAFBDJRdqNHMHgiYHaB";
//            String accessKeySecret = "******************************";
//
//            DefaultProfile defaultProfile = DefaultProfile.getProfile("cn-hangzhou", accessKeyId, accessKeySecret);
//            IAcsClient client = new DefaultAcsClient(defaultProfile);
//
//            Map<String, Object> input = new HashMap();
//            input.put("content", content);
//            input.put("domain", "home_daily");
//            input.put("entity", true);
//            Map<String, Object> map = new HashMap<>();
//            map.put("input", input);
//
//            RunPreTrainServiceRequest request = new RunPreTrainServiceRequest();
//            request.setServiceName("ReviewAnalysis");
//            request.setPredictContent(JSON.toJSONString(map));
//            RunPreTrainServiceResponse response = client.getAcsResponse(request);
//            System.out.println(response.getPredictResult());
//        } catch (ClientException e) {
//            log.error(e.getMessage(), e);
//        }
//
//        return null;
//    }

    public static String[] split(String content) {
        String[] cs = content.split("[,，。 ;；:：?？【】、\\\\/&|{}\\[\\]()]");
        System.out.println(cs);


        return cs;
    }

    public static int parsePolarityProb(String polarity, Double prob, Double threshold) {
        polarity = polarity.trim();
        if (StringUtils.equals(polarity, "正")) {
            if (prob > threshold) {
                return 1;
            }

        } else if (StringUtils.equals(polarity, "负")) {
            if (prob > threshold) {
                return -1;
            }
        }

        return 0;
    }

    //小红书 - 抖音
    // 为了性能，将正则表达式预编译为静态常量
    private static final Pattern PATH_PATTERN = Pattern.compile("/([0-9a-f]{24})");
    private static final Pattern QUERY_PARAM_PATTERN = Pattern.compile("target_note_id=([0-9a-f]{24})");
    private static final Pattern FALLBACK_PATTERN = Pattern.compile("([0-9a-f]{24})");

    public static String getXhsWorkId(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 1. 优先从URL路径中查找ID
        String pathPart = url;
        int queryStartIndex = url.indexOf('?');
        if (queryStartIndex != -1) {
            pathPart = url.substring(0, queryStartIndex);
        }
        Matcher pathMatch = PATH_PATTERN.matcher(pathPart);
        if (pathMatch.find()) {
            return pathMatch.group(1);
        }

        // 2. 如果路径中没有，则查找 target_note_id 查询参数
        Matcher queryMatch = QUERY_PARAM_PATTERN.matcher(url);
        if (queryMatch.find()) {
            return queryMatch.group(1);
        }

        // 3. 作为最后的备用方案，在整个URL中模糊搜索
        Matcher fallbackMatch = FALLBACK_PATTERN.matcher(url);
        if (fallbackMatch.find()) {
            return fallbackMatch.group(1);
        }

        return null;
    }


    public static String getDyWorkId(String url) {

        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 匹配连续的数字，长度至少10位
        Pattern pattern = Pattern.compile("\\d{10,}");
        Matcher matcher = pattern.matcher(url);

        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    public static String extractDyId(String url) {
        Pattern MODAL_ID_PATTERN = Pattern.compile("modal_id=(\\d{19})");
        Pattern URL_ID_PATTERN = Pattern.compile("/(\\d{19})\\b");
        Matcher modalMatcher = MODAL_ID_PATTERN.matcher(url);
        if (modalMatcher.find()) {
            return modalMatcher.group(1);
        }

        Matcher urlMatcher = URL_ID_PATTERN.matcher(url);
        if (urlMatcher.find()) {
            return urlMatcher.group(1);
        }

        return null;
    }

    /**
     * 切分清博的图片 url / video 地址
     *
     * @param input
     * @return
     */
    public static List<String> splitImgUrlToList(String input) {

        if (StringUtils.isBlank(input)) {
            return Collections.emptyList();
        }
        final String delimiterRegex = "[;]";

        return Arrays.stream(input.split(delimiterRegex))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toList());
    }


    public static void main(String[] args) {
        String url1 = "https://www.xiaohongshu.com/explore/65d5d89d000000002102da8c";
        String url2 = "http://xhslink.com/notes/65d5d89d000000002102da8c";
        String url3 = "https://www.xiaohongshu.com/discovery/item/6568846c000000000800d93f?share_from_user_hidden=true&app_version=8.23.0";
        String url4 = "https://www.xiaohongshu.com/notification?target_note_id=65c246f6000000000b027429&type=like&need_show_launch_app_guidance=true";
        String url5 = "xhsdiscover://note/65c8ea39000000002700e4blablabla"; // 假设的某种内部链接
        String url6 = "https://www.xiaohongshu.com/explore/683822750000000021018e02?xsec_token=ABSLcDfunWXkae6yXreKIMVjz4GLGS_qWu-4XGitlkXd8=&xsec_source=pc_feed"; // 假设的某种内部链接
        String invalidUrl = "https://www.xiaohongshu.com/some/other/path";

        System.out.println("URL1 ID: " + getXhsWorkId(url1)); // 期望: 65d5d89d000000002102da8c
        System.out.println("URL2 ID: " + getXhsWorkId(url2)); // 期望: 65d5d89d000000002102da8c
        System.out.println("URL3 ID: " + getXhsWorkId(url3)); // 期望: 6568846c000000000800d93f
        System.out.println("URL4 ID: " + getXhsWorkId(url4)); // 期望: 65c246f6000000000b027429
        System.out.println("URL5 ID: " + getXhsWorkId(url5)); // 期望: 65c8ea39000000002700e4b1
        System.out.println("URL6 ID: " + getXhsWorkId(url6)); // 期望: 683822750000000021018e02
        System.out.println("Invalid URL ID: " + getXhsWorkId(invalidUrl)); // 期望: null

    }

    public static String parseStr(JSONObject object, String key) {
        try {
            return object.getString(key);
        } catch (Exception e) {
            return "";
        }
    }

    public static List<String> parseUrlListV1(JSONArray imageList) {
        try {
            List<String> images = new ArrayList<>();

            if (imageList != null) {
                for (int index = 0; index < imageList.size(); index++) {
                    JSONObject urlObject = imageList.getJSONObject(index);
                    String url = urlObject.getString("url");
                    if (url.startsWith("http")) {
                        images.add(url);
                    } else {
                        images.add("https:" + url);
                    }
                }
            }

            return images;
        } catch (Exception e) {
            return null;
        }
    }


    public static List<String> parseUrlListV2(JSONArray imageList) {
        try {
            List<String> images = new ArrayList<>();

            if (imageList != null) {
                for (int index = 0; index < imageList.size(); index++) {
                    String url = imageList.getString(index);
                    if (url.startsWith("http")) {
                        images.add(url);
                    } else {
                        images.add("https:" + url);
                    }
                }
            }

            return images;
        } catch (Exception e) {
            return null;
        }
    }

    public static List<String> parseUrlListV2(JSONObject object, String key) {
        try {
            List<String> images = new ArrayList<>();

            try {
                JSONObject obj = object.getJSONObject(key);
                String url = obj.getString("url");
                if (url.startsWith("http")) {
                    images.add(url);
                } else {
                    images.add("https:" + url);
                }

            } catch (Exception e) {
                try {
                    JSONArray arr = object.getJSONArray(key);
                    return parseUrlListV1(arr);
                } catch (Exception e2) {
                    // 类型未知
                }
            }

            return images;
        } catch (Exception e) {
            return null;
        }
    }

    public static String parseUrlString(String url) {
        try {
            if (!url.startsWith("http")) {
                return "https:" + url;
            }

            return url;
        } catch (Exception e) {
            return url;
        }
    }

    public static LocalDateTime parseTimeStr(String t) {
        try {
            return DateTimeUtil.parseLocalDateTimeStr(t);
        } catch (Exception e) {
            return LocalDateTime.now();
        }
    }

    public static Integer parseInt(JSONObject object, String key) {
        try {
            return object.getInteger(key);
        } catch (Exception e) {
            return 0;
        }
    }

    public static Double parseDouble(JSONObject object, String key) {
        try {
            return object.getDouble(key);
        } catch (Exception e) {
            return 0.0;
        }
    }

    public static boolean containsChinese(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        String regex = ".*[\\u4E00-\\u9FFF].*"; // 汉字的 Unicode 范围
        return str.matches(regex) || str.chars().anyMatch(ch -> ch >= 0x4E00 && ch <= 0x9FFF);
    }

    public static String convertCity(String key) {
        String c = key.toLowerCase();
        return cityMap.getOrDefault(c, key);
    }

    private static final int DEFAULT_MAX_LENGTH = 9900;

    public static String truncate(String input) {
        return truncate(input, DEFAULT_MAX_LENGTH);
    }

    public static String truncate(String input, int maxLength) {
        // 1. 检查 maxLength 参数是否合法
        if (maxLength < 0) {
            throw new IllegalArgumentException("最大长度不能为负数: " + maxLength);
        }

        // 2. 检查输入字符串是否为 null
        if (input == null) {
            return null;
        }

        // 3. 如果字符串长度未超过最大长度，直接返回原字符串，避免不必要的对象创建
        if (input.length() <= maxLength) {
            return input;
        }

        // 4. 执行截断操作
        return input.substring(0, maxLength) + "......";
    }
}
