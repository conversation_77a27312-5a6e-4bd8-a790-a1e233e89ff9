package com.bq.linkcore.biz;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.bq.linkcore.bean.entity.AtTiktokAuthorPoolDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorProfileRecordDO;
import com.bq.linkcore.bean.entity.AtTiktokAuthorSearchResultDO;
import com.bq.linkcore.bean.entity.UserTenantRelDO;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorPoolMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorProfileRecordMapper;
import com.bq.linkcore.dao.mapper.AtTiktokAuthorSearchResultMapper;
import com.bq.linkcore.dao.mapper.UserTenantRelMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
public class TiktokAuthorBiz {

    @Resource
    private AtTiktokAuthorPoolMapper atTiktokAuthorPoolMapper;
    @Resource
    private AtTiktokAuthorProfileRecordMapper atTiktokAuthorProfileRecordMapper;
    @Resource
    private AtTiktokAuthorSearchResultMapper atTiktokAuthorSearchResultMapper;

    public int insertTiktokAuthor(AtTiktokAuthorPoolDO atTiktokAuthorPoolDO) {
        return atTiktokAuthorPoolMapper.insert(atTiktokAuthorPoolDO);
    }

    public int updateTiktokAuthor(AtTiktokAuthorPoolDO atTiktokAuthorPoolDO) {
        return atTiktokAuthorPoolMapper.updateById(atTiktokAuthorPoolDO);
    }

    public AtTiktokAuthorPoolDO queryTiktokAuthorByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorPoolDO::getAuthorId, authorId);

        return atTiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    public AtTiktokAuthorPoolDO queryTiktokAuthorByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorPoolDO::getUniqueId, uniqueId);

        return atTiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    public AtTiktokAuthorPoolDO queryTiktokAuthorBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorPoolDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorPoolDO::getSecUid, secUid);

        return atTiktokAuthorPoolMapper.selectOne(queryWrapper);
    }

    public int insertTiktokAuthorHis(AtTiktokAuthorProfileRecordDO atTiktokAuthorProfileRecordDO) {
        return atTiktokAuthorProfileRecordMapper.insert(atTiktokAuthorProfileRecordDO);
    }

    public List<AtTiktokAuthorProfileRecordDO> queryTiktokAuthorHisByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorProfileRecordDO::getAuthorId, authorId);

        return atTiktokAuthorProfileRecordMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorProfileRecordDO> queryTiktokAuthorHisByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorProfileRecordDO::getUniqueId, uniqueId);

        return atTiktokAuthorProfileRecordMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorProfileRecordDO> queryTiktokAuthorHisBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorProfileRecordDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorProfileRecordDO::getSecUid, secUid);

        return atTiktokAuthorProfileRecordMapper.selectList(queryWrapper);
    }

    public int insertTiktokAuthorSearchResultDO(AtTiktokAuthorSearchResultDO atTiktokAuthorSearchResultDO) {
        return atTiktokAuthorSearchResultMapper.insert(atTiktokAuthorSearchResultDO);
    }

    public List<AtTiktokAuthorSearchResultDO> queryTiktokAuthorSearchResultDOByAuthorId(String authorId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorSearchResultDO::getAuthorId, authorId);

        return atTiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorSearchResultDO> queryTiktokAuthorSearchResultDOByUniqueId(String uniqueId) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorSearchResultDO::getUniqueId, uniqueId);

        return atTiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }

    public List<AtTiktokAuthorSearchResultDO> queryTiktokAuthorSearchResultDOBySecUid(String secUid) {
        LambdaQueryWrapper<AtTiktokAuthorSearchResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AtTiktokAuthorSearchResultDO::getSecUid, secUid);

        return atTiktokAuthorSearchResultMapper.selectList(queryWrapper);
    }
    
}
