package com.bq.linkcore.common;

public enum ResponseMsg {


    FAIL("-1", "操作失败"),
    SUCCESS("0", "操作成功"),

    ERROR_INVALID_PHONE("10001", "错误的手机号格式"),
    ERROR_INVALID_PARAMS("10002", "非法的参数"),
    ERROR_PHONE_MESSAGE("10003", "获取手机短信失败"),
    ERROR_FREQUENTLY("10004", "调用接口频繁"),
    ERROR_SEND_MESSAGE("10005", "发送短信失败"),
    ERROR_PASSWORD("10006", "密码错误"),
    ERROR_INVALID_MESSAGE("10007", "短信验证码无效"),
    ERROR_LOGIN_ERR_CODE_TIMES("10008", "验证码输入错误超过%d次，请%d分钟后重试"),
    ERROR_LOGIN_ERR_PWD_TIMES("10009", "密码输入错误超过%d次，请%d分钟后重试"),

    ERROR_NO_ACCOUNT("10010", "账户不存在"),
    ERROR_CREATE_ACCOUNT("10011", "账户创建失败"),
    ERROR_ACCOUNT_OR_PWD("10012", "请检查账户或密码"),
    ERROR_UNKNOWN_TOKEN("10013", "token错误"),
    ERROR_NO_PERMISSION("10015", "账户无权限访问"),
    ERROR_NOT_MEMBER("10016", "暂无权限，请联系服务顾问"),
    ERROR_OVER_THRESHOLD("10017", "超过阈值，请联系客服"),

    ERROR_PASSWORD_ERROR("10025", "密码错误，请联系管理员"),
    ERROR_PASSWORD_NOT_EXIST("10038", "密码格式错误"),
    ERROR_USER_UPDATE("10026", "用户信息更新失败"),
    ERROR_PASSWORD_01("10027", "请输入8-16个字符，不能以空格开头或结尾"),
    ERROR_PASSWORD_02("10028", "密码包含不支持的特殊字符，请重试"),
    ERROR_PASSWORD_03("10029", "包含大小写字母、数字、特殊字符中的三种"),

    ERROR_TASK_IS_RUNNING("10030", "任务正在监控中"),
    ERROR_TASK_DATE("10031", "任务的日期无效"),

    ERROR_NOT_USER_INFO("10034", "获取用户信息失败"),
    ERROR_NO_PHONE("10035", "错误的手机号"),

    ERROR_NO_TASK("10036", "无效任务"),

    ERROR_NO_TENANT("10037", "未找到对应租户"),

    ERROR_TASK_MONITORING("10050", "正在监控中，不能删除"),

    ERROR_QRCODE_STATUS("10051", "授权状态错误"),

    ERROR_NO_AVAILABLE_AGENT("10052", "没有可用的agent"),
    ERROR_QRCODE_NO_DATA("10053", "no author data"),

    ERROR_INVALID_TYPE("10100", "无效的数据类型"),

    ERROR_UPDATE_DATABASE("10101", "更新数据库错误"),

    ERROR_INVALID_OAUTH("10300", "无效的授权用户"),

    ERROR_INVALID_VIDEO("10301", "未找到达人视频"),

    ERROR_INVALID_VIDEO_URL("10302", "未知的视频url地址"),

    ERROR_NOT_APPOINT_TENANT("10303", "企业未指定agent"),

    // =============== 8开头支付相关 start =========================

    FAIL_PAYMENT_TYPE_ERROR("80000", "不支持的支付方式"),

    FAIL_ORDER_PAYMENT_EXPIRED("80001", "支付过期"),

    FAIL_ORDER_STATUS_ERROR("80002", "订单状态异常"),

    FAIL_PAYMENT_NET_ERROR("80100", "网络异常"),

    FAIL_PAYMENT_API_ERROR("80101", "支付API异常"),

    FAIL_PAYMENT_CALL_ERROR("80102", "支付调用异常"),

    FAIL_WECHAT_PAY_NO_RSP("80200", "微信支付未返回数据"),

    FAIL_WECHAT_PAY_NEW_ORDER_FAIL("80201", "微信支付发起支付失败"),

    FAIL_CAPACITY_NOT_ENOUGH("80300", "剩余容量不足"),


    // =============== 8开头支付相关 end =========================
    ERROR_DOUYIN_AUTHOR_NOT_EXIST("30001", "抖音达人不存在"),

    ERROR_DOUYIN_AUTHOR_ORDER_TYPE("30002", "暂不支持此类型排序"),
    ERROR_DOUYIN_AUTHOR_USER_EXPORT_TOO_MANY("30003", "导出人数过多，请稍后再试"),
    ERROR_DOUYIN_AUTHOR_USER_COMPARE_LIMIT("30003", "比较个数限制"),

    ERROR_DOUYIN_PRODUCT_INDEX_NULL("30004", "商品 ID 不能为空"),
    ERROR_DOUYIN_PRODUCT_NOT_EXIST("30005", "商品 ID 不能为空"),

    ERROR_DOUYIN_PRODUCT_PRICE("30006", "最高价格不能低于最低价格"),

    ERROR_DOUYIN_AUTH_ERROR("40001", "抖音认证失败"),

    ERROR_OAUTH_TYPE_ERROR("40002", "暂无该类型认证"),

    ERROR_OAUTH_CODE_NULL("40003", "错误的授权code"),

    ERROR_DOUYIN_LIVE_MONITOR_TIME("40004", "非法的监控时间设置"),

    ERROR_DOUYIN_USER_ACCESS_ERROR("40005", "非法的用户权限"),

    ERROR_DOUYIN_MONITOR_ROOM_ID("40006", "暂无本直播场次信息"),
    ERROR_CHART_DELETE("ERROR_CHART_DELETE", "删除临时表数据出错"),

    ERROR_THIRD_PART_AUTH_CODE_ERROR("40007", "无效的授权码"),

    ERROR_ADD_MONITOR_TASK("40008", "所选时段存在相同达人监控，无法重复添加。"),

    ERROR_USER_AUTH_DATE_TYPE("40008", "无效的type"),

    ERROR_ADD_RULE_REPEAT("40009","规则重复！"),
    ERROR_ADD_RULE_ID("40010","未知的规则ID！"),

    ERROR_VIDEO_MONITOR_TEMPLATE_NOT_EXIST("50001", "未知的模板code"),
    ERROR_VIDEO_MONITOR_TEMPLATE_REPEAT("50002", "模板重复"),


    ERROR_VIDEO_CLUE_FOLLOW_UP_NOT_EXIST("50003", "无该线索跟进信息"),

    ERROR_VIDEO_CLUE_FOLLOW_GROUP_LIMIT("50004", "超过最大分组限制"),
    ERROR_VIDEO_CLUE_FOLLOW_GROUP_FORMAT("50005", "分组名称格式错误"),
    ERROR_VIDEO_MONITOR_OPINION_FLAG_ERROR("50005", "错误的视频分类场景"),

    ERROR_ACCOUNT_EXIST("50006", "账号已经存在"),
    ERROR_ACCOUNT_NOT_EXIST("50007", "账号不存在"),

    ERROR_DATE_TIME_FORMAT("50008", "日期出错"),

    ERROR_NO_ROLE("60001", "无该角色code"),

    ERROR_NOT_INVITE_USER("60002", "邀请码不存在"),
    ERROR_INVITE_CODE_INVALID("60003", "无效的邀请码"),

    ERROR_USER_INVITE("60004", "生成邀请码错误"),

    ERROR_INVITE_NO_AUTH("60005", "无该用户权限"),

    ERROR_INVITE_USER_EXIST("60006", "邀请用户已存在"),

    ERROR_LOGIN_TYPE("60007", "无效的登陆类型"),

    ERROR_USER_ROLE_EXIST("60008", "用户角色已存在"),
    ERROR_TENANT_REPEAT_EXIST("60009", "用户角色已存在"),

    ERROR_MONITOR_TEMPLATE_NAME_REPEAT("60010", "模板名称或监控品牌词重复"),
    ERROR_MONITOR_TEMPLATE("60011", "模板添加失败"),
    ERROR_TEMPLATE_NOT_EXIST("60012", "模板不存在"),
    ERROR_UPDATE_TEMPLATE_FAIL("60013", "模板更新失败"),
    ERROR_TEMPLATE_DEL_FAILED("60014", "模板删除失败"),
    ERROR_TEMPLATE_REL_NOTE_EXIST("60015", "关联code不存在"),

    ERROR_AUTH_URL("60016", "邀请链接失效"),
    ERROR_AUTHORIZING_LIMIT("60017", "同时授权人数过多，请稍后再试！"),


    ERROR_ROLE_NAME_ERROR("70002", "角色名称不能为空！"),
    FAIL_AUTH_ERROR_ILLEGAL_ARGUMENT("70003", "参数错误"),
    FAIL_AUTH_ERROR_TENANT("70004", "租户不匹配！"),

    ERROR_AUTH_ERROR_ROLE_NAME("70005", "角色名已存在！"),

    ERROR_UPDATE_ROLE_INFO("70006", "更新角色失败！"),

    ERROR_ROLE_NOT_EXIST("70008", "角色信息不存在！"),

    ERROR_ROLE_BIND_USER("70009", "存在用户绑定了该角色！"),

    ERROR_GET_OPEN_API_ACCESS_TOKEN("90001", "获取access token失败！"),
    ERROR_OPEN_API_ACCESS_TOKEN("90002", "access token无效"),
    ERROR_OPEN_API("90003", "获取数据失败"),

    ERROR_OPEN_API_NO_QUOTA("90005", "已没有调用额度，请联系客服"),

    ERROR_OPEN_API_ADD_ARTICLE_LISTENER("90006", "添加文件监控失败，请稍后重试"),

    ERROR_OPEN_API_NO_ARTICLE_DETAIL("90007", "未匹配到作品详情数据"),
    ERROR_USER_NO_QUOTA("90008", "已没有调用额度,调用失败"),

    ERROR_OPEN_API_NO_AUTHOR_DETAIL("90009", "未匹配到作者数据"),

    ERROR_OPEN_API_AUTHOR_LISTENER_EXIST("90010", "存在相同的监控账户"),

    ERROR_INTERFACE_CURRENT_LIMITING("900011", "接口限流"),

    ERROR_FAIL_ADD_AT_SETTING("90012", "添加监控失败"),

    ERROR_FAIL_ADD_AT_DEV("90013", "dev环境不允许真实注册监控"),

    ERROR_WK_NOT_EXIST_WORK("90014", "作品不存在"),
    ERROR_WK_NOT_EXIST_LABEL("90015", "评论未定义标签"),
    ERROR_WK_URL_ILLEGAL("90016", "非法的链接"),
    ERROR_NO_ENOUGH_QUOTA("90017", "调用额度不足，请联系客服"),

    ERROR_KW_WEBHOOK_FORMAT("90018", "关键词webhook格式错误"),

    ERROR_KW_NOT_EXIST("90019", "关键词不存在"),

    ERROR_KW_IS_EXIST("90020", "关键词已经存在"),

    ERROR_KW_DATA_NOT_EXIST("90021", "关键词无数据，请选择刷新重试"),

    ERROR_SUBMIT_RECORD_REPEAT("90022", "提交作品重复！")
    ;

    private String code;
    private String msg;


    ResponseMsg(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}